#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CMOST (Colorectal Microsimulation Outcomes Screening Tool) - 统一入口文件

这是CMOST项目的统一入口文件，提供多种启动方式：
1. 命令行界面 (CLI)
2. 图形用户界面 (GUI)
3. 交互式Python环境
4. 快速仿真示例

使用方法:
    python main.py                    # 显示帮助信息
    python main.py --cli              # 启动命令行界面
    python main.py --gui              # 启动图形界面
    python main.py --interactive      # 启动交互式环境
    python main.py --demo             # 运行快速演示
    python main.py --version          # 显示版本信息

作者: <PERSON>, <PERSON><PERSON>, et al.
版本: 1.0.0
"""

import sys
import os
import argparse
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

try:
    import cmost
    from cmost import __version__, __author__
    from cmost.config import settings, initialize_settings
except ImportError as e:
    print(f"错误: 无法导入CMOST模块: {e}")
    print("请确保已正确安装CMOST包:")
    print("  pip install -e .")
    sys.exit(1)


def setup_logging(verbose=False):
    """设置日志配置"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def show_banner():
    """显示CMOST横幅信息"""
    banner = f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║   ██████╗███╗   ███╗ ██████╗ ███████╗████████╗                              ║
║  ██╔════╝████╗ ████║██╔═══██╗██╔════╝╚══██╔══╝                              ║
║  ██║     ██╔████╔██║██║   ██║███████╗   ██║                                 ║
║  ██║     ██║╚██╔╝██║██║   ██║╚════██║   ██║                                 ║
║  ╚██████╗██║ ╚═╝ ██║╚██████╔╝███████║   ██║                                 ║
║   ╚═════╝╚═╝     ╚═╝ ╚═════╝ ╚══════╝   ╚═╝                                 ║
║                                                                              ║
║  Colorectal Microsimulation Outcomes Screening Tool                         ║
║  结直肠癌微观仿真筛查结果工具                                                ║
║                                                                              ║
║  版本: {__version__:<20} 作者: {__author__:<30} ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)


def launch_cli():
    """启动命令行界面"""
    print("启动CMOST命令行界面...")
    print("使用 'cmost --help' 查看所有可用命令")
    print("\n常用命令:")
    print("  cmost simulate --patients 10000 --output results.json")
    print("  cmost compare --strategies colonoscopy,fit --output comparison.json")
    print("  cmost calibrate --target-data data.csv --output params.json")
    print("\n要直接使用CLI，请运行: cmost")
    
    # 导入并启动CLI
    from cmost.cli import main as cli_main
    try:
        cli_main()
    except SystemExit:
        pass


def launch_gui():
    """启动图形用户界面"""
    print("启动CMOST图形用户界面...")
    
    try:
        import tkinter as tk
        from cmost.ui.main_window import MainWindow
        
        # 创建主窗口
        root = tk.Tk()
        root.title("CMOST - 结直肠癌筛查仿真工具")
        root.geometry("1200x800")
        
        # 设置窗口图标（如果存在）
        try:
            icon_path = project_root / "cmost" / "resources" / "icon.ico"
            if icon_path.exists():
                root.iconbitmap(str(icon_path))
        except Exception:
            pass
        
        # 创建主应用
        app = MainWindow(root)
        
        print("图形界面已启动！")
        print("您可以通过界面进行以下操作：")
        print("1. 配置仿真参数")
        print("2. 选择筛查策略")
        print("3. 运行仿真")
        print("4. 查看结果和图表")
        
        # 启动主循环
        root.mainloop()
        
    except ImportError as e:
        print(f"错误: 无法启动图形界面: {e}")
        print("请确保已安装tkinter:")
        print("  sudo apt-get install python3-tk  # Ubuntu/Debian")
        print("  或使用conda: conda install tk")
        sys.exit(1)
    except Exception as e:
        print(f"启动图形界面时发生错误: {e}")
        sys.exit(1)


def launch_interactive():
    """启动交互式Python环境"""
    print("启动CMOST交互式环境...")
    print("已导入常用模块，您可以直接使用:")
    print("  - simulation: 仿真引擎")
    print("  - settings: 配置管理")
    print("  - Settings: 配置类")
    print("  - np: NumPy")
    print("  - pd: Pandas")
    
    # 导入常用模块到全局命名空间
    import numpy as np
    import pandas as pd
    from cmost.core.simulation import Simulation
    from cmost.config.settings import Settings
    
    # 创建示例设置
    example_settings = Settings()
    example_settings.set('Number_patients', 10000)
    
    print("\n示例代码:")
    print(">>> settings = Settings()")
    print(">>> settings.set('Number_patients', 10000)")
    print(">>> simulation = Simulation(settings)")
    print(">>> results = simulation.run(years=50)")
    print(">>> stats = simulation.get_summary_statistics()")
    
    # 启动交互式环境
    import code
    code.interact(
        banner="",
        local={
            'simulation': Simulation,
            'settings': settings,
            'Settings': Settings,
            'np': np,
            'pd': pd,
            'example_settings': example_settings
        }
    )


def run_demo():
    """运行快速演示"""
    print("运行CMOST快速演示...")

    try:
        # 简化的演示，只展示配置和基本功能
        from cmost.config.settings import Settings

        print("CMOST配置演示:")
        print("=" * 50)

        # 创建演示设置
        demo_settings = Settings()

        print("1. 基本设置:")
        print(f"   版本: {demo_settings.get('Version', 'N/A')}")
        print(f"   患者数量选项: {demo_settings.get('NumberPatientsValues', 'N/A')}")
        print(f"   结果路径: {demo_settings.get('ResultsPath', 'N/A')}")

        print("\n2. 模型参数示例:")
        model_params = demo_settings.get('ModelParameters', {})
        print(f"   早期腺瘤发生率倍数: {model_params.get('early_mult', 'N/A')}")
        print(f"   进展期腺瘤发生率倍数: {model_params.get('adv_mult', 'N/A')}")
        print(f"   癌症发生率倍数: {model_params.get('cancer_mult', 'N/A')}")

        print("\n3. 筛查设置示例:")
        screening_params = demo_settings.get('Screening', {})
        print(f"   启用筛查: {screening_params.get('EnableScreening', 'N/A')}")
        print(f"   筛查年龄: {screening_params.get('ScreeningAges', 'N/A')}")
        print(f"   筛查依从性: {screening_params.get('ScreeningCompliance', 'N/A')}")

        print("\n4. 配置文件支持:")
        print("   支持的格式: JSON, YAML, MATLAB (.mat), Excel (.xlsx)")
        print("   可以通过以下方式加载配置:")
        print("   - settings.load_settings('config.json')")
        print("   - settings.load_settings('config.yaml')")
        print("   - settings.load_settings('config.xlsx')")

        print("\n5. Excel模板生成:")
        try:
            from cmost.config.excel_templates import create_excel_template
            template_path = "demo_config_template.xlsx"
            success = create_excel_template('basic', template_path)
            if success:
                print(f"   ✓ Excel配置模板已生成: {template_path}")
            else:
                print("   ✗ Excel模板生成失败")
        except ImportError:
            print("   ✗ Excel功能需要安装: pip install pandas openpyxl")

        print("\n演示完成！")
        print("\n下一步操作:")
        print("1. 使用图形界面: python main.py --gui")
        print("2. 使用命令行: python main.py --cli")
        print("3. 交互式环境: python main.py --interactive")
        print("4. 生成Excel模板: python -m cmost.cli config --template excel --output my_config.xlsx")

    except Exception as e:
        print(f"演示运行失败: {e}")
        print("\n这可能是由于缺少某些依赖包造成的。")
        print("请确保已安装所有必需的依赖:")
        print("  pip install -e .")
        print("  pip install pandas openpyxl  # 用于Excel支持")
        sys.exit(1)


def show_help():
    """显示帮助信息"""
    help_text = """
CMOST使用指南:

启动选项:
  --cli, -c          启动命令行界面
  --gui, -g          启动图形用户界面
  --interactive, -i  启动交互式Python环境
  --demo, -d         运行快速演示
  --version, -v      显示版本信息
  --help, -h         显示此帮助信息

配置选项:
  --config FILE      指定配置文件路径
  --verbose          启用详细输出

示例:
  python main.py --gui                    # 启动图形界面
  python main.py --cli                    # 启动命令行
  python main.py --demo                   # 运行演示
  python main.py --config my_config.json # 使用指定配置

更多信息:
  文档: https://cmost.readthedocs.io
  源码: https://github.com/misselwitz/cmost
  问题: https://github.com/misselwitz/cmost/issues
    """
    print(help_text)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="CMOST - 结直肠癌微观仿真筛查结果工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python main.py --gui                    # 启动图形界面
  python main.py --cli                    # 启动命令行
  python main.py --demo                   # 运行演示
  python main.py --config my_config.json # 使用指定配置
        """
    )
    
    # 启动选项
    group = parser.add_mutually_exclusive_group()
    group.add_argument('--cli', '-c', action='store_true', help='启动命令行界面')
    group.add_argument('--gui', '-g', action='store_true', help='启动图形用户界面')
    group.add_argument('--interactive', '-i', action='store_true', help='启动交互式Python环境')
    group.add_argument('--demo', '-d', action='store_true', help='运行快速演示')
    group.add_argument('--version', '-v', action='store_true', help='显示版本信息')
    
    # 配置选项
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--verbose', action='store_true', help='启用详细输出')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.verbose)
    
    # 显示横幅
    show_banner()
    
    # 初始化配置
    if args.config:
        if os.path.exists(args.config):
            initialize_settings(args.config)
            print(f"已加载配置文件: {args.config}")
        else:
            print(f"警告: 配置文件不存在: {args.config}")
    
    # 根据参数执行相应操作
    if args.version:
        print(f"CMOST版本: {__version__}")
        print(f"作者: {__author__}")
        return
    
    if args.cli:
        launch_cli()
    elif args.gui:
        launch_gui()
    elif args.interactive:
        launch_interactive()
    elif args.demo:
        run_demo()
    else:
        show_help()


if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n发生未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
